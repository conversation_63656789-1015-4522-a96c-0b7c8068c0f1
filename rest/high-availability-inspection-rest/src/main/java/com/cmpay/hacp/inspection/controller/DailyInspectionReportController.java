package com.cmpay.hacp.inspection.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.application.service.DailyInspectionReportService;
import com.cmpay.hacp.inspection.assembler.DailyInspectionReportDTOMapper;
import com.cmpay.hacp.inspection.domain.model.report.DailyInspectionReport;
import com.cmpay.hacp.inspection.domain.model.report.DailyInspectionReportDetail;
import com.cmpay.hacp.inspection.dto.DailyInspectionReportDetailRspDTO;
import com.cmpay.hacp.inspection.dto.DailyInspectionReportQueryReqDTO;
import com.cmpay.hacp.inspection.dto.DailyInspectionReportRspDTO;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 按日巡检报告控制器
 */
@Api(tags = "按日巡检报告管理")
@RestController
@RequestMapping("/inspection/daily-report")
@RequiredArgsConstructor
@Slf4j
public class DailyInspectionReportController {

    private final DailyInspectionReportService dailyInspectionReportService;
    private final DailyInspectionReportDTOMapper dailyInspectionReportDTOMapper;

    /**
     * 分页查询按日巡检报告列表
     *
     * @param reqDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询按日巡检报告列表", notes = "根据条件分页查询按日巡检报告列表")
    @ApiResponse(code = 200, message = "成功")
    @PreAuthorize("hasPermission('DailyInspectionReportController','inspection:daily-report:query')")
    public DefaultRspDTO<PageDTO<DailyInspectionReportRspDTO>> getReportPage(
            @Validated @RequestBody DailyInspectionReportQueryReqDTO reqDTO) {
        
        log.info("分页查询按日巡检报告列表: {}", reqDTO);

        // 转换查询条件
        DailyInspectionReport queryCondition = dailyInspectionReportDTOMapper.toDailyInspectionReport(reqDTO);

        // 执行分页查询
        IPage<DailyInspectionReport> page = dailyInspectionReportService.getReportPage(
                reqDTO.getPage(), queryCondition);

        // 转换结果
        List<DailyInspectionReportRspDTO> rspDTOList = dailyInspectionReportDTOMapper
                .toDailyInspectionReportRspDTOList(page.getRecords());

        // 构建分页响应
        PageDTO<DailyInspectionReportRspDTO> result = new PageDTO<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal()
        );
        result.setRecords(rspDTOList);

        log.info("查询到 {} 条按日巡检报告记录", page.getTotal());
        return DefaultRspDTO.newSuccessInstance(result);
    }

    /**
     * 获取按日巡检报告详情
     *
     * @param reportId 报告ID
     * @return 报告详情
     */
    @GetMapping("/detail/{reportId}")
    @ApiOperation(value = "获取按日巡检报告详情", notes = "获取指定按日巡检报告的基本信息")
    @ApiResponse(code = 200, message = "成功")
    @PreAuthorize("hasPermission('DailyInspectionReportController','inspection:daily-report:query')")
    public DefaultRspDTO<DailyInspectionReportRspDTO> getReportDetail(
            @ApiParam(name = "reportId", value = "报告ID", required = true)
            @PathVariable("reportId") String reportId) {

        log.info("获取按日巡检报告详情: reportId={}", reportId);

        // 获取报告详情
        DailyInspectionReport report = dailyInspectionReportService.getReportDetail(reportId);

        if (report == null) {
            log.warn("按日巡检报告不存在: reportId={}", reportId);
            return DefaultRspDTO.newFailInstance("报告不存在");
        }

        // 转换为响应DTO
        DailyInspectionReportRspDTO rspDTO = dailyInspectionReportDTOMapper.toDailyInspectionReportRspDTO(report);

        return DefaultRspDTO.newSuccessInstance(rspDTO);
    }

    /**
     * 获取按日巡检报告详细内容
     *
     * @param reportId 报告ID
     * @return 报告详细内容
     */
    @GetMapping("/detail-content/{reportId}")
    @ApiOperation(value = "获取按日巡检报告详细内容", notes = "获取指定按日巡检报告的详细分析内容")
    @ApiResponse(code = 200, message = "成功")
    @PreAuthorize("hasPermission('DailyInspectionReportController','inspection:daily-report:query')")
    public DefaultRspDTO<DailyInspectionReportDetailRspDTO> getReportDetailContent(
            @ApiParam(name = "reportId", value = "报告ID", required = true)
            @PathVariable("reportId") String reportId) {

        log.info("获取按日巡检报告详细内容: reportId={}", reportId);

        // 获取报告详细内容
        DailyInspectionReportDetail detail = dailyInspectionReportService.getReportDetailContent(reportId);

        if (detail == null) {
            log.warn("按日巡检报告详细内容不存在: reportId={}", reportId);
            return DefaultRspDTO.newFailInstance("报告详细内容不存在");
        }

        // 转换为响应DTO
        DailyInspectionReportDetailRspDTO rspDTO = dailyInspectionReportDTOMapper
                .toDailyInspectionReportDetailRspDTO(detail);

        return DefaultRspDTO.newSuccessInstance(rspDTO);
    }
}
