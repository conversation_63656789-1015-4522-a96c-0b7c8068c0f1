package com.cmpay.hacp.inspection.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * 按日巡检报告响应DTO
 */
@Data
@ApiModel(description = "按日巡检报告响应DTO")
public class DailyInspectionReportRspDTO {

    @ApiModelProperty(value = "报告日期", example = "2025-03-24")
    private LocalDate reportDate;

    @ApiModelProperty(value = "日报告编号", example = "DR20250324")
    private String reportId;

    @ApiModelProperty(value = "规则数", example = "15")
    private Integer ruleCount;

    @ApiModelProperty(value = "执行数（总检查数）", example = "150")
    private Integer executionCount;

    @ApiModelProperty(value = "告警数", example = "5")
    private Integer warningCount;

    @ApiModelProperty(value = "错误数", example = "3")
    private Integer errorCount;

    @ApiModelProperty(value = "通过率（百分比）", example = "92.2")
    private Double passRate;

    @ApiModelProperty(value = "通过率变化（相对昨日，百分点）", example = "1.5")
    private Double passRateChange;

    @ApiModelProperty(value = "平均响应时间（毫秒）", example = "2500")
    private Long averageResponseTime;

    @ApiModelProperty(value = "报告状态", example = "COMPLETED")
    private String reportStatus;

    @ApiModelProperty(value = "审计信息")
    private AuditInfoDTO auditInfo;
}
