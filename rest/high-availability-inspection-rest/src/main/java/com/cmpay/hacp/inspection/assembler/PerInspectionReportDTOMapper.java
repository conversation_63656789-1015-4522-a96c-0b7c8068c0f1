package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.dto.PerInspectionReportDetailRspDTO;
import com.cmpay.hacp.inspection.dto.PerInspectionReportQueryReqDTO;
import com.cmpay.hacp.inspection.dto.PerInspectionReportRspDTO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PerInspectionReportDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PerInspectionReportDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 按次巡检报告对象转换器
 */
@Mapper(componentModel = "spring")
public interface PerInspectionReportDTOMapper {

    /**
     * 查询请求DTO转DO
     *
     * @param reqDTO 查询请求DTO
     * @return DO对象
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdByName", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedByName", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "taskExecutionId", ignore = true)
    @Mapping(target = "executionDuration", ignore = true)
    @Mapping(target = "executionTimeStr", ignore = true)
    @Mapping(target = "resultStats", ignore = true)
    @Mapping(target = "generateTime", ignore = true)
    PerInspectionReportDO toPerInspectionReportDO(PerInspectionReportQueryReqDTO reqDTO);

    /**
     * DO转响应DTO
     *
     * @param reportDO DO对象
     * @return 响应DTO
     */
    @Mapping(target = "auditInfo.createdBy", source = "createdBy")
    @Mapping(target = "auditInfo.createdByName", source = "createdByName")
    @Mapping(target = "auditInfo.createdTime", source = "createdTime")
    @Mapping(target = "auditInfo.updatedBy", source = "updatedBy")
    @Mapping(target = "auditInfo.updatedByName", source = "updatedByName")
    @Mapping(target = "auditInfo.updatedTime", source = "updatedTime")
    PerInspectionReportRspDTO toPerInspectionReportRspDTO(PerInspectionReportDO reportDO);

    /**
     * DO列表转响应DTO列表
     *
     * @param reportDOList DO列表
     * @return 响应DTO列表
     */
    List<PerInspectionReportRspDTO> toPerInspectionReportRspDTOList(List<PerInspectionReportDO> reportDOList);

    /**
     * 详细内容DO转响应DTO
     *
     * @param detailDO 详细内容DO
     * @return 详细内容响应DTO
     */
    PerInspectionReportDetailRspDTO toPerInspectionReportDetailRspDTO(PerInspectionReportDetailDO detailDO);

    /**
     * 执行概况转换
     */
    PerInspectionReportDetailRspDTO.ExecutionSummaryDTO toExecutionSummaryDTO(
            PerInspectionReportDetailDO.ExecutionSummary executionSummary);

    /**
     * 执行结果分布转换
     */
    PerInspectionReportDetailRspDTO.ExecutionDistributionDTO toExecutionDistributionDTO(
            PerInspectionReportDetailDO.ExecutionDistribution executionDistribution);

    /**
     * 执行时间分布转换
     */
    PerInspectionReportDetailRspDTO.ExecutionTimeDistributionDTO toExecutionTimeDistributionDTO(
            PerInspectionReportDetailDO.ExecutionTimeDistribution timeDistribution);

    /**
     * 规则检查详情列表转换
     */
    List<PerInspectionReportDetailRspDTO.RuleCheckDetailDTO> toRuleCheckDetailDTOList(
            List<PerInspectionReportDetailDO.RuleCheckDetail> ruleCheckDetails);

    /**
     * 异常详情列表转换
     */
    List<PerInspectionReportDetailRspDTO.ExceptionDetailDTO> toExceptionDetailDTOList(
            List<PerInspectionReportDetailDO.ExceptionDetail> exceptionDetails);

    /**
     * 状态数量列表转换
     */
    List<PerInspectionReportDetailRspDTO.ExecutionDistributionDTO.StatusCountDTO> toStatusCountDTOList(
            List<PerInspectionReportDetailDO.ExecutionDistribution.StatusCount> statusCounts);

    /**
     * 时间区间列表转换
     */
    List<PerInspectionReportDetailRspDTO.ExecutionTimeDistributionDTO.TimeRangeDTO> toTimeRangeDTOList(
            List<PerInspectionReportDetailDO.ExecutionTimeDistribution.TimeRange> timeRanges);
}
