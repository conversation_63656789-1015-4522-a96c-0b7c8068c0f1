package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.domain.model.report.PerInspectionReport;
import com.cmpay.hacp.inspection.domain.model.report.PerInspectionReportDetail;
import com.cmpay.hacp.inspection.dto.PerInspectionReportDetailRspDTO;
import com.cmpay.hacp.inspection.dto.PerInspectionReportQueryReqDTO;
import com.cmpay.hacp.inspection.dto.PerInspectionReportRspDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 按次巡检报告对象转换器
 */
@Mapper(componentModel = "spring")
public interface PerInspectionReportDTOMapper {

    /**
     * 查询请求DTO转领域对象
     *
     * @param reqDTO 查询请求DTO
     * @return 领域对象
     */
    @Mapping(target = "taskExecutionId", ignore = true)
    @Mapping(target = "executionDuration", ignore = true)
    @Mapping(target = "executionTimeStr", ignore = true)
    @Mapping(target = "resultStats", ignore = true)
    @Mapping(target = "generateTime", ignore = true)
    @Mapping(target = "auditInfo", ignore = true)
    PerInspectionReport toPerInspectionReport(PerInspectionReportQueryReqDTO reqDTO);

    /**
     * 领域对象转响应DTO
     *
     * @param report 领域对象
     * @return 响应DTO
     */
    PerInspectionReportRspDTO toPerInspectionReportRspDTO(PerInspectionReport report);

    /**
     * 领域对象列表转响应DTO列表
     *
     * @param reportList 领域对象列表
     * @return 响应DTO列表
     */
    List<PerInspectionReportRspDTO> toPerInspectionReportRspDTOList(List<PerInspectionReport> reportList);

    /**
     * 详细内容领域对象转响应DTO
     *
     * @param detail 详细内容领域对象
     * @return 详细内容响应DTO
     */
    PerInspectionReportDetailRspDTO toPerInspectionReportDetailRspDTO(PerInspectionReportDetail detail);

    /**
     * 执行概况转换
     */
    PerInspectionReportDetailRspDTO.ExecutionSummaryDTO toExecutionSummaryDTO(
            PerInspectionReportDetail.ExecutionSummary executionSummary);

    /**
     * 执行结果分布转换
     */
    PerInspectionReportDetailRspDTO.ExecutionDistributionDTO toExecutionDistributionDTO(
            PerInspectionReportDetail.ExecutionDistribution executionDistribution);

    /**
     * 执行时间分布转换
     */
    PerInspectionReportDetailRspDTO.ExecutionTimeDistributionDTO toExecutionTimeDistributionDTO(
            PerInspectionReportDetail.ExecutionTimeDistribution timeDistribution);

    /**
     * 规则检查详情列表转换
     */
    List<PerInspectionReportDetailRspDTO.RuleCheckDetailDTO> toRuleCheckDetailDTOList(
            List<PerInspectionReportDetail.RuleCheckDetail> ruleCheckDetails);

    /**
     * 异常详情列表转换
     */
    List<PerInspectionReportDetailRspDTO.ExceptionDetailDTO> toExceptionDetailDTOList(
            List<PerInspectionReportDetail.ExceptionDetail> exceptionDetails);

    /**
     * 状态数量列表转换
     */
    List<PerInspectionReportDetailRspDTO.ExecutionDistributionDTO.StatusCountDTO> toStatusCountDTOList(
            List<PerInspectionReportDetail.ExecutionDistribution.StatusCount> statusCounts);

    /**
     * 时间区间列表转换
     */
    List<PerInspectionReportDetailRspDTO.ExecutionTimeDistributionDTO.TimeRangeDTO> toTimeRangeDTOList(
            List<PerInspectionReportDetail.ExecutionTimeDistribution.TimeRange> timeRanges);
}
