package com.cmpay.hacp.inspection.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * 按日巡检报告详细内容响应DTO
 */
@Data
@ApiModel(description = "按日巡检报告详细内容响应DTO")
public class DailyInspectionReportDetailRspDTO {

    @ApiModelProperty(value = "报告ID", example = "DR20250324")
    private String reportId;

    @ApiModelProperty(value = "巡检分类统计（用于饼图展示）")
    private List<CategoryStatisticsDTO> categoryStatistics;

    @ApiModelProperty(value = "近7日通过率趋势（用于柱状图展示）")
    private List<DailyPassRateTrendDTO> recentPassRateTrends;

    @ApiModelProperty(value = "异常详情列表（异常项标签页）")
    private ExceptionDetailsDTO exceptionDetails;

    @ApiModelProperty(value = "趋势分析数据（趋势分析标签页）")
    private TrendAnalysisDTO trendAnalysis;

    /**
     * 巡检分类统计DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "巡检分类统计")
    public static class CategoryStatisticsDTO {
        @ApiModelProperty(value = "分类名称", example = "数据库")
        private String categoryName;

        @ApiModelProperty(value = "该分类的检查数量", example = "25")
        private Integer checkCount;

        @ApiModelProperty(value = "占比（百分比）", example = "16.7")
        private Double percentage;
    }

    /**
     * 近7日通过率趋势DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "近7日通过率趋势")
    public static class DailyPassRateTrendDTO {
        @ApiModelProperty(value = "日期", example = "2025-03-24")
        private LocalDate date;

        @ApiModelProperty(value = "通过率", example = "92.5")
        private Double passRate;

        @ApiModelProperty(value = "通过率级别", example = "良好")
        private String level;
    }

    /**
     * 异常详情DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "异常详情")
    public static class ExceptionDetailsDTO {
        @ApiModelProperty(value = "异常总数", example = "8")
        private Integer totalExceptions;

        @ApiModelProperty(value = "高优先级异常数", example = "2")
        private Integer highPriorityExceptions;

        @ApiModelProperty(value = "中优先级异常数", example = "3")
        private Integer mediumPriorityExceptions;

        @ApiModelProperty(value = "低优先级异常数", example = "3")
        private Integer lowPriorityExceptions;

        @ApiModelProperty(value = "异常详情列表")
        private List<ExceptionItemDTO> exceptionItems;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @ApiModel(description = "异常项")
        public static class ExceptionItemDTO {
            @ApiModelProperty(value = "异常类型", example = "性能异常")
            private String exceptionType;

            @ApiModelProperty(value = "异常描述", example = "CPU使用率持续超过90%")
            private String description;

            @ApiModelProperty(value = "影响资源", example = "服务器-001, 服务器-002")
            private String affectedResources;

            @ApiModelProperty(value = "优先级", example = "HIGH")
            private String priority;

            @ApiModelProperty(value = "建议措施", example = "建议优化应用程序或增加服务器资源")
            private String suggestion;
        }
    }

    /**
     * 趋势分析DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel(description = "趋势分析")
    public static class TrendAnalysisDTO {
        @ApiModelProperty(value = "总体趋势", example = "稳定")
        private String overallTrend;

        @ApiModelProperty(value = "通过率变化趋势", example = "上升")
        private String passRateTrend;

        @ApiModelProperty(value = "响应时间变化趋势", example = "稳定")
        private String responseTimeTrend;

        @ApiModelProperty(value = "异常数量变化趋势", example = "下降")
        private String exceptionCountTrend;

        @ApiModelProperty(value = "关键指标分析")
        private List<KeyMetricAnalysisDTO> keyMetrics;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @ApiModel(description = "关键指标分析")
        public static class KeyMetricAnalysisDTO {
            @ApiModelProperty(value = "指标名称", example = "平均通过率")
            private String metricName;

            @ApiModelProperty(value = "当前值", example = "92.5")
            private String currentValue;

            @ApiModelProperty(value = "变化趋势", example = "+2.3%")
            private String changeTrend;

            @ApiModelProperty(value = "分析说明", example = "相比上周，通过率有所提升")
            private String analysis;
        }
    }
}
