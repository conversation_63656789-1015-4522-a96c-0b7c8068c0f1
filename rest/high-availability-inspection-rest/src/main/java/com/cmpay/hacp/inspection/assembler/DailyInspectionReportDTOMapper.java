package com.cmpay.hacp.inspection.assembler;

import com.cmpay.hacp.inspection.dto.DailyInspectionReportDetailRspDTO;
import com.cmpay.hacp.inspection.dto.DailyInspectionReportQueryReqDTO;
import com.cmpay.hacp.inspection.dto.DailyInspectionReportRspDTO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyInspectionReportDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyInspectionReportDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 按日巡检报告对象转换器
 */
@Mapper(componentModel = "spring")
public interface DailyInspectionReportDTOMapper {

    /**
     * 查询请求DTO转DO
     *
     * @param reqDTO 查询请求DTO
     * @return DO对象
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdByName", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedByName", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    @Mapping(target = "ruleCount", ignore = true)
    @Mapping(target = "executionCount", ignore = true)
    @Mapping(target = "warningCount", ignore = true)
    @Mapping(target = "errorCount", ignore = true)
    @Mapping(target = "passRateChange", ignore = true)
    @Mapping(target = "averageResponseTime", ignore = true)
    @Mapping(target = "reportStatus", source = "reportStatus")
    DailyInspectionReportDO toDailyInspectionReportDO(DailyInspectionReportQueryReqDTO reqDTO);

    /**
     * DO转响应DTO
     *
     * @param reportDO DO对象
     * @return 响应DTO
     */
    @Mapping(target = "auditInfo.createdBy", source = "createdBy")
    @Mapping(target = "auditInfo.createdByName", source = "createdByName")
    @Mapping(target = "auditInfo.createdTime", source = "createdTime")
    @Mapping(target = "auditInfo.updatedBy", source = "updatedBy")
    @Mapping(target = "auditInfo.updatedByName", source = "updatedByName")
    @Mapping(target = "auditInfo.updatedTime", source = "updatedTime")
    DailyInspectionReportRspDTO toDailyInspectionReportRspDTO(DailyInspectionReportDO reportDO);

    /**
     * DO列表转响应DTO列表
     *
     * @param reportDOList DO列表
     * @return 响应DTO列表
     */
    List<DailyInspectionReportRspDTO> toDailyInspectionReportRspDTOList(List<DailyInspectionReportDO> reportDOList);

    /**
     * 详细内容DO转响应DTO
     *
     * @param detailDO 详细内容DO
     * @return 详细内容响应DTO
     */
    DailyInspectionReportDetailRspDTO toDailyInspectionReportDetailRspDTO(DailyInspectionReportDetailDO detailDO);

    /**
     * 分类统计列表转换
     */
    List<DailyInspectionReportDetailRspDTO.CategoryStatisticsDTO> toCategoryStatisticsDTOList(
            List<DailyInspectionReportDetailDO.CategoryStatistics> categoryStatistics);

    /**
     * 通过率趋势列表转换
     */
    List<DailyInspectionReportDetailRspDTO.DailyPassRateTrendDTO> toDailyPassRateTrendDTOList(
            List<DailyInspectionReportDetailDO.DailyPassRateTrend> recentPassRateTrends);

    /**
     * 异常详情转换
     */
    DailyInspectionReportDetailRspDTO.ExceptionDetailsDTO toExceptionDetailsDTO(
            DailyInspectionReportDetailDO.ExceptionDetails exceptionDetails);

    /**
     * 趋势分析转换
     */
    DailyInspectionReportDetailRspDTO.TrendAnalysisDTO toTrendAnalysisDTO(
            DailyInspectionReportDetailDO.TrendAnalysis trendAnalysis);

    /**
     * 异常项列表转换
     */
    List<DailyInspectionReportDetailRspDTO.ExceptionDetailsDTO.ExceptionItemDTO> toExceptionItemDTOList(
            List<DailyInspectionReportDetailDO.ExceptionDetails.ExceptionItem> exceptionItems);

    /**
     * 关键指标分析列表转换
     */
    List<DailyInspectionReportDetailRspDTO.TrendAnalysisDTO.KeyMetricAnalysisDTO> toKeyMetricAnalysisDTOList(
            List<DailyInspectionReportDetailDO.TrendAnalysis.KeyMetricAnalysis> keyMetrics);
}
