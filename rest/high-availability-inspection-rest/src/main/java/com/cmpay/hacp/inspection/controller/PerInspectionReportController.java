package com.cmpay.hacp.inspection.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.application.service.PerInspectionReportService;
import com.cmpay.hacp.inspection.assembler.PerInspectionReportDTOMapper;
import com.cmpay.hacp.inspection.domain.model.report.PerInspectionReport;
import com.cmpay.hacp.inspection.domain.model.report.PerInspectionReportDetail;
import com.cmpay.hacp.inspection.dto.PerInspectionReportDetailRspDTO;
import com.cmpay.hacp.inspection.dto.PerInspectionReportQueryReqDTO;
import com.cmpay.hacp.inspection.dto.PerInspectionReportRspDTO;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 按次巡检报告控制器
 */
@Api(tags = "按次巡检报告管理")
@RestController
@RequestMapping("/inspection/per-report")
@RequiredArgsConstructor
@Slf4j
public class PerInspectionReportController {

    private final PerInspectionReportService perInspectionReportService;
    private final PerInspectionReportDTOMapper perInspectionReportDTOMapper;

    /**
     * 分页查询按次巡检报告列表
     *
     * @param reqDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询按次巡检报告列表", notes = "根据条件分页查询按次巡检报告列表")
    @ApiResponse(code = 200, message = "成功")
    @PreAuthorize("hasPermission('PerInspectionReportController','inspection:per-report:query')")
    public DefaultRspDTO<PageDTO<PerInspectionReportRspDTO>> getReportPage(
            @Validated @RequestBody PerInspectionReportQueryReqDTO reqDTO) {
        
        log.info("分页查询按次巡检报告列表: {}", reqDTO);

        // 转换查询条件
        PerInspectionReport queryCondition = perInspectionReportDTOMapper.toPerInspectionReport(reqDTO);

        // 执行分页查询
        IPage<PerInspectionReport> page = perInspectionReportService.getReportPage(
                reqDTO.getPage(), queryCondition);

        // 转换结果
        List<PerInspectionReportRspDTO> rspDTOList = perInspectionReportDTOMapper
                .toPerInspectionReportRspDTOList(page.getRecords());

        // 构建分页响应
        PageDTO<PerInspectionReportRspDTO> result = new PageDTO<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal()
        );
        result.setRecords(rspDTOList);

        log.info("查询到 {} 条按次巡检报告记录", page.getTotal());
        return DefaultRspDTO.newSuccessInstance(result);
    }

    /**
     * 获取按次巡检报告详情
     *
     * @param reportId 报告ID
     * @return 报告详情
     */
    @GetMapping("/detail/{reportId}")
    @ApiOperation(value = "获取按次巡检报告详情", notes = "获取指定按次巡检报告的基本信息")
    @ApiResponse(code = 200, message = "成功")
    @PreAuthorize("hasPermission('PerInspectionReportController','inspection:per-report:query')")
    public DefaultRspDTO<PerInspectionReportRspDTO> getReportDetail(
            @ApiParam(name = "reportId", value = "报告ID", required = true)
            @PathVariable("reportId") String reportId) {

        log.info("获取按次巡检报告详情: reportId={}", reportId);

        // 获取报告详情
        PerInspectionReport report = perInspectionReportService.getReportDetail(reportId);

        if (report == null) {
            log.warn("按次巡检报告不存在: reportId={}", reportId);
            return DefaultRspDTO.newFailInstance("报告不存在");
        }

        // 转换为响应DTO
        PerInspectionReportRspDTO rspDTO = perInspectionReportDTOMapper.toPerInspectionReportRspDTO(report);

        return DefaultRspDTO.newSuccessInstance(rspDTO);
    }

    /**
     * 获取按次巡检报告详细内容
     *
     * @param reportId 报告ID
     * @return 报告详细内容
     */
    @GetMapping("/detail-content/{reportId}")
    @ApiOperation(value = "获取按次巡检报告详细内容", notes = "获取指定按次巡检报告的详细分析内容")
    @ApiResponse(code = 200, message = "成功")
    @PreAuthorize("hasPermission('PerInspectionReportController','inspection:per-report:query')")
    public DefaultRspDTO<PerInspectionReportDetailRspDTO> getReportDetailContent(
            @ApiParam(name = "reportId", value = "报告ID", required = true)
            @PathVariable("reportId") String reportId) {

        log.info("获取按次巡检报告详细内容: reportId={}", reportId);

        // 获取报告详细内容
        PerInspectionReportDetail detail = perInspectionReportService.getReportDetailContent(reportId);

        if (detail == null) {
            log.warn("按次巡检报告详细内容不存在: reportId={}", reportId);
            return DefaultRspDTO.newFailInstance("报告详细内容不存在");
        }

        // 转换为响应DTO
        PerInspectionReportDetailRspDTO rspDTO = perInspectionReportDTOMapper
                .toPerInspectionReportDetailRspDTO(detail);

        return DefaultRspDTO.newSuccessInstance(rspDTO);
    }
}
