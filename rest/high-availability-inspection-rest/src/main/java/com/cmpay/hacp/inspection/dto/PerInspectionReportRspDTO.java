package com.cmpay.hacp.inspection.dto;

import com.cmpay.hacp.inspection.domain.model.enums.ExecutionStatus;
import com.cmpay.hacp.inspection.domain.model.enums.TriggerMode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 按次巡检报告响应DTO
 */
@Data
@ApiModel(description = "按次巡检报告响应DTO")
public class PerInspectionReportRspDTO {

    @ApiModelProperty(value = "报告ID", example = "RPT-202503240003")
    private String reportId;

    @ApiModelProperty(value = "关联的任务执行记录ID", example = "EXEC-202503240003")
    private String taskExecutionId;

    @ApiModelProperty(value = "任务ID", example = "TASK-000001")
    private String taskId;

    @ApiModelProperty(value = "任务名称", example = "网络连通性检测")
    private String taskName;

    @ApiModelProperty(value = "触发方式：0-定时触发，1-手动触发", example = "0")
    private TriggerMode triggerMode;

    @ApiModelProperty(value = "执行状态", example = "2")
    private ExecutionStatus executionStatus;

    @ApiModelProperty(value = "开始时间", example = "2025-03-24T10:00:00")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间", example = "2025-03-24T10:05:30")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "执行耗时（毫秒）", example = "330000")
    private Long executionDuration;

    @ApiModelProperty(value = "执行耗时（格式化字符串）", example = "5分30秒")
    private String executionTimeStr;

    @ApiModelProperty(value = "执行结果统计", example = "{\"passed\": 3, \"warning\": 1, \"failed\": 2}")
    private String resultStats;

    @ApiModelProperty(value = "报告生成时间", example = "2025-03-24T10:06:00")
    private LocalDateTime generateTime;

    @ApiModelProperty(value = "审计信息")
    private AuditInfoDTO auditInfo;
}
