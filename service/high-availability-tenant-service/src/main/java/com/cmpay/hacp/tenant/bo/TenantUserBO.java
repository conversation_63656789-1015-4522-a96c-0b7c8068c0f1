
package com.cmpay.hacp.tenant.bo;

import com.cmpay.hacp.annotation.mybatis.CryptField;
import com.cmpay.hacp.annotation.mybatis.CryptType;
import lombok.Data;

@Data
public class TenantUserBO {

    /**
     * @Fields tenantId 租户ID
     */
    private String tenantId;
    /**
     * @Fields tenantName 租户名称
     */
    private String tenantName;
    /**
     * @Fields tenantUserType 租户用户类型(1-租户管理员，0-普通用户)
     */
    private String tenantUserType;

    /**
     * @Fields userId 用户编号
     */
    private String userId;

    /**
     * @Fields userName 用户名
     */
    private String userName;

    /**
     * @Fields fullName 姓名
     */
    @CryptField(type = CryptType.SM4)
    private String fullName;

    /**
     * @Fields mobile 手机号
     */
    @CryptField(type = CryptType.SM4)
    private String mobile;

    /**
     * @Fields userStatus 租户成员用户状态
     */
    private String userStatus;
}