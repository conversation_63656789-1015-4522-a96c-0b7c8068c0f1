
package com.cmpay.hacp.tenant.bo;

import com.cmpay.hacp.annotation.mybatis.CryptField;
import com.cmpay.hacp.annotation.mybatis.CryptType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class TenantBO {
    /**
     * @Fields tenantId 租户ID
     */
    private String tenantId;
    /**
     * @Fields tenantName 租户名称
     */
    private String tenantName;
    /**
     * @Fields deptId 部门编号
     */
    private String deptId;
    /**
     * @Fields deptName 部门名称
     */
    private String deptName;
    /**
     * @Fields createUser 创建者
     */
    private String createUser;
    /**
     * @Fields createTime 创建时间
     */
    private LocalDateTime createTime;
    /**
     * @Fields updateUser 更新者
     */
    private String updateUser;
    /**
     * @Fields updateTime 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * @Fields remarks 备注信息
     */
    private String remarks;
    /**
     * @Fields status 删除标记
     */
    private String status;
    /**
     * @Fields tenantUserType 租户成员类型
     */
    private String tenantUserType;
    /**
     * @Fields userId 租户管理员ID
     */
    private String userId;
    /**
     * @Fields userName 租户管理员用户名
     */
    private String userName;
    /**
     * @Fields fullName 租户管理员姓名
     */
    @CryptField(type = CryptType.SM4)
    private String fullName;
    /**
     * @Fields email 租户管理员邮箱
     */
    @CryptField(type = CryptType.SM4)
    private String email;
    /**
     * @Fields mobile 租户管理员手机
     */
    @CryptField(type = CryptType.SM4)
    private String mobile;

    /**
     * @Fields userStatus 租户管理员用户状态
     */
    private String userStatus;
    /**
     * @Fields workspaces 项目列表
     */
    private List<TenantWorkspaceBO> workspaces;

}
