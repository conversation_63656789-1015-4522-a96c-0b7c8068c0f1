package com.cmpay.hacp.system.log.service.impl;

import com.cmpay.hacp.system.bo.system.SystemLogBO;
import com.cmpay.hacp.system.dao.ISystemLogExtDao;
import com.cmpay.hacp.system.entity.SystemLogDO;
import com.cmpay.hacp.system.log.bo.StaticDataSizeBO;
import com.cmpay.hacp.system.log.bo.SysStaticLogBO;
import com.cmpay.hacp.system.log.service.SystemStaticLogService;
import com.cmpay.hacp.utils.IdGenUtil;
import com.cmpay.lemon.common.context.LemonContext;
import com.cmpay.lemon.common.utils.BeanUtils;
import com.cmpay.lemon.common.utils.JudgeUtils;
import com.cmpay.lemon.framework.page.PageInfo;
import com.cmpay.lemon.framework.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * 系统日志管理
 */
@Service
@Slf4j
public class SystemLogServiceAdapter implements SystemStaticLogService {

    @Resource
    @Lazy
    private ISystemLogExtDao riseSystemLogExtDao;


    @Override
    public void setStaticRspDataSize(long rspDataSize, String rspDataSizeType) {
        StaticDataSizeBO staticDataSizeBO = new StaticDataSizeBO();
        staticDataSizeBO.setRspDataSizeType(rspDataSizeType);
        staticDataSizeBO.setRspDataSize(rspDataSize);
        LemonContext.getCurrentContext().put(STATIC_RSP_DATA_SIZE, staticDataSizeBO);
    }


    @Override
    public StaticDataSizeBO getStaticRspDataSize() {
        StaticDataSizeBO staticDataSizeBO = LemonContext.getCurrentContext().containsKey(STATIC_RSP_DATA_SIZE) ? (StaticDataSizeBO) LemonContext.getCurrentContext().get(STATIC_RSP_DATA_SIZE) : null;
        LemonContext.getCurrentContext().remove(STATIC_RSP_DATA_SIZE);
        return staticDataSizeBO;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void add(SysStaticLogBO sysStaticLogBO) {
        try {
            SystemLogBO riseSystemLog = new SystemLogBO();
            BeanUtils.copyProperties(riseSystemLog, sysStaticLogBO);
            SystemLogDO riseSystemLogEntity = new SystemLogDO();
            BeanUtils.copyProperties(riseSystemLogEntity, riseSystemLog);
            if (JudgeUtils.isNotBlank(riseSystemLog.getLogId())) {
                riseSystemLogEntity.setRequestId(riseSystemLog.getLogId());
            }
            riseSystemLogEntity.setId(IdGenUtil.generatorLogId());
            riseSystemLogExtDao.insert(riseSystemLogEntity);
        } catch (Exception e) {
            log.warn("RiseSystemLogServiceImpl add Exception {}", e.getMessage());
        }
    }

    @Override
    public PageInfo list(int pageNum, int pageSize, SysStaticLogBO sysStaticLogBO) {
        SystemLogBO systemLog = new SystemLogBO();
        BeanUtils.copyProperties(systemLog, sysStaticLogBO);
        PageInfo<SystemLogBO> pageInfo = PageUtils.pageQueryWithCount(pageNum, pageSize, () -> riseSystemLogExtDao.getSystemLogs(systemLog));
        if (JudgeUtils.isNotEmpty(pageInfo.getList())) {
            pageInfo.getList().forEach(systemLogInfo -> {
                if (JudgeUtils.isNotNull(systemLogInfo)) {
                    systemLogInfo.setIsDynamic(JudgeUtils.isNotEmpty(systemLogInfo.getDynamicLogs()) ? 1 : 0);
                }
            });
        }
        return pageInfo;
    }

    @Override
    public SysStaticLogBO info(String id) {
        SystemLogBO systemLogInfo = riseSystemLogExtDao.getSystemLogInfo(id);
        if (JudgeUtils.isNotNull(systemLogInfo)) {
            systemLogInfo.setIsDynamic(JudgeUtils.isNotEmpty(systemLogInfo.getDynamicLogs()) ? 1 : 0);
        }
        return systemLogInfo;
    }

    @Override
    public void delete(List<String> ids) {
        riseSystemLogExtDao.deleteByIds(ids);
    }


}
