package com.cmpay.hacp.system.auth;

import com.cmpay.hacp.enums.MsgEnum;
import com.cmpay.lemon.framework.data.DefaultRspDTO;
import com.cmpay.lemon.framework.response.ResponseMessageResolver;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * session失效处理
 *
 * @author: lihuiquan
 */
public class HacpWebAdminAuthenticationLogoutEntryPoint implements AuthenticationEntryPoint {

    private final ResponseMessageResolver responseMessageResolver;

    public HacpWebAdminAuthenticationLogoutEntryPoint(ResponseMessageResolver responseMessageResolver) {
        this.responseMessageResolver = responseMessageResolver;
    }

    @Override
    public void commence(HttpServletRequest request,
            HttpServletResponse response,
            AuthenticationException authException) throws IOException {
        if (!RequestMethod.OPTIONS.name().equalsIgnoreCase(request.getMethod().toUpperCase())) {
            expiredResponse(request, response);
        }
    }

    private void expiredResponse(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setStatus(HttpServletResponse.SC_OK);
        this.responseMessageResolver.resolveResponse(request, response, new DefaultRspDTO(MsgEnum.LOGIN_SESSION_EXPIRE));
    }
}
