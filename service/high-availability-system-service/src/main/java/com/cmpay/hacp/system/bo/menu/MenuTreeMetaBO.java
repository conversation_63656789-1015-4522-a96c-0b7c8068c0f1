package com.cmpay.hacp.system.bo.menu;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 菜单树 RSP 元 DTO
 *
 * <AUTHOR>
 * @create 2024/05/09 15:12:28
 * @since 1.0.0
 */
@Data
public class MenuTreeMetaBO {

    private List<MenuTreeMetaBO> children;
    /**
     * 父菜单ID
     */
    private Long parentId;
    /**
     * 父菜单名字
     */
    private String parentName;

    /**
     * 菜单类型
     */
    private String type;

    /**
     * 菜单ID
     */
    private Long menuId;
    /**
     * 菜单名称
     */
    private String name;
    /**
     * 菜单url
     */
    private String url;
    /**
     * 权限
     */
    private String perms;
    /**
     * 菜单图标
     */
    private String icon;
    /**
     * 排序序号
     */
    private Long orderNum;
    /**
     * HTML文档元数据
     */
    private Map meta;
    /**
     * 对应组件
     */
    private String component;
    /**
     * 映射
     */
    private String redirect;
    /**
     * 别名
     */
    private String enName;

    /**
     * 隐藏标题
     */
    private Boolean hideTitle;
    /**
     * 显示
     */
    private Boolean hidden;
    /**
     * 隐藏子菜单
     */
    private Boolean hideChildren;

    /**
     * 是否缓存
     */
    private Boolean keepalive;

    /**
     * 隐藏页面标题栏
     */
    private Boolean hidePageTitleBar;

}
