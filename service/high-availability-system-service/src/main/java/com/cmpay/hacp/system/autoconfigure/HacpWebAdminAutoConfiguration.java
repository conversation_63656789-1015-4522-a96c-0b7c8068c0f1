package com.cmpay.hacp.system.autoconfigure;

import com.cmpay.hacp.system.properties.HacpWebAdminProperties;
import com.cmpay.hacp.system.service.SystemCacheService;
import com.cmpay.hacp.system.service.impl.HazelcastCacheServiceImpl;
import com.cmpay.hacp.system.service.impl.RedisCacheServiceImpl;
import com.hazelcast.core.HazelcastInstance;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;

@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties({HacpWebAdminProperties.class})
@ConditionalOnClass({HacpWebAdminProperties.class})
@Slf4j
public class HacpWebAdminAutoConfiguration {

    /**
     * 系统缓存服务
     *
     * @param redisTemplate Redis 模板 {@link com.cmpay.hacp.client.RedisCacheClient}
     * @return {@link SystemCacheService}
     */
    @Bean
    @ConditionalOnProperty(prefix = "hacp.web.admin", name = "cache-type", havingValue = "redis")
    @ConditionalOnClass({RedisTemplate.class, RedisCacheServiceImpl.class})
    public SystemCacheService systemCacheService(@Qualifier("cacheRedisTemplate") RedisTemplate redisTemplate) {
        log.info("lemon-web-admin-cache-redis is enable");
        return new RedisCacheServiceImpl(redisTemplate);
    }

    @Bean
    @ConditionalOnProperty(prefix = "hacp.web.admin", name = "cache-type", havingValue = "hazelcast", matchIfMissing = true)
    @ConditionalOnClass({HazelcastInstance.class, HazelcastCacheServiceImpl.class})
    public SystemCacheService hazelcastCacheServiceImpl(HazelcastInstance hazelcastInstance) {
        log.info("lemon-web-admin-cache-hazelcast is enable");
        return new HazelcastCacheServiceImpl(hazelcastInstance);
    }


}
