package com.cmpay.hacp.inspection.application.assembler;

import com.cmpay.hacp.inspection.domain.model.report.DailyInspectionReport;
import com.cmpay.hacp.inspection.domain.model.report.DailyInspectionReportDetail;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyInspectionReportDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyInspectionReportDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 按日巡检报告领域对象转换器
 * 用于领域对象和数据对象之间的转换
 */
@Mapper(componentModel = "spring")
public interface DailyInspectionReportMapper {

    /**
     * 领域对象转DO（用于查询条件）
     *
     * @param report 领域对象
     * @return DO对象
     */
    @IgnoreAuditFields
    DailyInspectionReportDO toDailyInspectionReportDO(DailyInspectionReport report);

    /**
     * DO转领域对象
     *
     * @param reportDO DO对象
     * @return 领域对象
     */
    @Mapping(target = "auditInfo.createdBy", source = "createdByName")
    @Mapping(target = "auditInfo.createdTime", source = "createdTime")
    @Mapping(target = "auditInfo.updatedBy", source = "updatedByName")
    @Mapping(target = "auditInfo.updatedTime", source = "updatedTime")
    DailyInspectionReport toDailyInspectionReport(DailyInspectionReportDO reportDO);

    /**
     * DO列表转领域对象列表
     *
     * @param reportDOList DO列表
     * @return 领域对象列表
     */
    List<DailyInspectionReport> toDailyInspectionReportList(List<DailyInspectionReportDO> reportDOList);

    /**
     * 详细内容DO转领域对象
     *
     * @param detailDO 详细内容DO
     * @return 详细内容领域对象
     */
    DailyInspectionReportDetail toDailyInspectionReportDetail(DailyInspectionReportDetailDO detailDO);

    /**
     * 分类统计列表转换
     */
    List<DailyInspectionReportDetail.CategoryStatistics> toCategoryStatisticsList(
            List<DailyInspectionReportDetailDO.CategoryStatistics> categoryStatistics);

    /**
     * 通过率趋势列表转换
     */
    List<DailyInspectionReportDetail.DailyPassRateTrend> toDailyPassRateTrendList(
            List<DailyInspectionReportDetailDO.DailyPassRateTrend> recentPassRateTrends);

    /**
     * 异常详情转换
     */
    DailyInspectionReportDetail.ExceptionDetails toExceptionDetails(
            DailyInspectionReportDetailDO.ExceptionDetails exceptionDetails);

    /**
     * 趋势分析转换
     */
    DailyInspectionReportDetail.TrendAnalysis toTrendAnalysis(
            DailyInspectionReportDetailDO.TrendAnalysis trendAnalysis);

    /**
     * 异常项列表转换
     */
    List<DailyInspectionReportDetail.ExceptionDetails.ExceptionItem> toExceptionItemList(
            List<DailyInspectionReportDetailDO.ExceptionDetails.ExceptionItem> exceptionItems);

    /**
     * 关键指标分析列表转换
     */
    List<DailyInspectionReportDetail.TrendAnalysis.KeyMetricAnalysis> toKeyMetricAnalysisList(
            List<DailyInspectionReportDetailDO.TrendAnalysis.KeyMetricAnalysis> keyMetrics);
}
