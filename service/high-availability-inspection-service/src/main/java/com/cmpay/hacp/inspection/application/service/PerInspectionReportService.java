package com.cmpay.hacp.inspection.application.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.domain.model.report.PerInspectionReport;
import com.cmpay.hacp.inspection.domain.model.report.PerInspectionReportDetail;

/**
 * 按次巡检报告查询服务接口
 * 负责查询和展示按次巡检报告数据
 */
public interface PerInspectionReportService {

    /**
     * 分页查询按次巡检报告列表
     *
     * @param page 分页参数
     * @param queryCondition 查询条件
     * @return 分页结果
     */
    IPage<PerInspectionReport> getReportPage(PageDTO<?> page, PerInspectionReport queryCondition);

    /**
     * 根据报告ID获取按次巡检报告详情
     *
     * @param reportId 报告ID
     * @return 报告详情，包含基本信息和详细内容
     */
    PerInspectionReport getReportDetail(String reportId);

    /**
     * 根据报告ID获取按次巡检报告详细内容
     *
     * @param reportId 报告ID
     * @return 报告详细内容
     */
    PerInspectionReportDetail getReportDetailContent(String reportId);
}
