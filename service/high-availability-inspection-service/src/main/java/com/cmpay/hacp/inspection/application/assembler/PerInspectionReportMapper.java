package com.cmpay.hacp.inspection.application.assembler;

import com.cmpay.hacp.inspection.domain.model.report.PerInspectionReport;
import com.cmpay.hacp.inspection.domain.model.report.PerInspectionReportDetail;
import com.cmpay.hacp.inspection.infrastructure.config.IgnoreAuditFields;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PerInspectionReportDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PerInspectionReportDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * 按次巡检报告领域对象转换器
 * 用于领域对象和数据对象之间的转换
 */
@Mapper(componentModel = "spring")
public interface PerInspectionReportMapper {

    /**
     * 领域对象转DO（用于查询条件）
     *
     * @param report 领域对象
     * @return DO对象
     */
    @IgnoreAuditFields
    PerInspectionReportDO toPerInspectionReportDO(PerInspectionReport report);

    /**
     * DO转领域对象
     *
     * @param reportDO DO对象
     * @return 领域对象
     */
    @Mapping(target = "auditInfo.createdBy", source = "createdByName")
    @Mapping(target = "auditInfo.createdTime", source = "createdTime")
    @Mapping(target = "auditInfo.updatedBy", source = "updatedByName")
    @Mapping(target = "auditInfo.updatedTime", source = "updatedTime")
    PerInspectionReport toPerInspectionReport(PerInspectionReportDO reportDO);

    /**
     * DO列表转领域对象列表
     *
     * @param reportDOList DO列表
     * @return 领域对象列表
     */
    List<PerInspectionReport> toPerInspectionReportList(List<PerInspectionReportDO> reportDOList);

    /**
     * 详细内容DO转领域对象
     *
     * @param detailDO 详细内容DO
     * @return 详细内容领域对象
     */
    PerInspectionReportDetail toPerInspectionReportDetail(PerInspectionReportDetailDO detailDO);

    /**
     * 执行概况转换
     */
    PerInspectionReportDetail.ExecutionSummary toExecutionSummary(
            PerInspectionReportDetailDO.ExecutionSummary executionSummary);

    /**
     * 执行结果分布转换
     */
    PerInspectionReportDetail.ExecutionDistribution toExecutionDistribution(
            PerInspectionReportDetailDO.ExecutionDistribution executionDistribution);

    /**
     * 执行时间分布转换
     */
    PerInspectionReportDetail.ExecutionTimeDistribution toExecutionTimeDistribution(
            PerInspectionReportDetailDO.ExecutionTimeDistribution timeDistribution);

    /**
     * 规则检查详情列表转换
     */
    List<PerInspectionReportDetail.RuleCheckDetail> toRuleCheckDetailList(
            List<PerInspectionReportDetailDO.RuleCheckDetail> ruleCheckDetails);

    /**
     * 异常详情列表转换
     */
    List<PerInspectionReportDetail.ExceptionDetail> toExceptionDetailList(
            List<PerInspectionReportDetailDO.ExceptionDetail> exceptionDetails);

    /**
     * 状态数量列表转换
     */
    List<PerInspectionReportDetail.ExecutionDistribution.StatusCount> toStatusCountList(
            List<PerInspectionReportDetailDO.ExecutionDistribution.StatusCount> statusCounts);

    /**
     * 时间区间列表转换
     */
    List<PerInspectionReportDetail.ExecutionTimeDistribution.TimeRange> toTimeRangeList(
            List<PerInspectionReportDetailDO.ExecutionTimeDistribution.TimeRange> timeRanges);
}
