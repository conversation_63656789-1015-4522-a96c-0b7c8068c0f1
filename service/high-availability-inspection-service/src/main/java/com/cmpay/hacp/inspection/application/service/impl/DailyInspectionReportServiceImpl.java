package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.application.service.DailyInspectionReportService;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyInspectionReportDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.DailyInspectionReportDetailDO;
import com.cmpay.hacp.inspection.infrastructure.repository.DailyInspectionReportDetailRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.DailyInspectionReportRepository;
import com.cmpay.lemon.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 按日巡检报告查询服务实现
 * 基于按日汇总表提供查询功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DailyInspectionReportServiceImpl implements DailyInspectionReportService {

    private final DailyInspectionReportRepository dailyInspectionReportRepository;
    private final DailyInspectionReportDetailRepository dailyInspectionReportDetailRepository;

    @Override
    public IPage<DailyInspectionReportDO> getReportPage(PageDTO<?> page, DailyInspectionReportDO queryCondition) {
        log.info("Querying daily inspection reports with page: {}, condition: {}", page, queryCondition);

        // 构建查询条件
        var queryWrapper = Wrappers.lambdaQuery(DailyInspectionReportDO.class);

        if (queryCondition != null) {
            // 报告ID模糊查询
            if (StringUtils.isNotBlank(queryCondition.getReportId())) {
                queryWrapper.like(DailyInspectionReportDO::getReportId, queryCondition.getReportId());
            }

            // 报告日期精确查询
            if (queryCondition.getReportDate() != null) {
                queryWrapper.eq(DailyInspectionReportDO::getReportDate, queryCondition.getReportDate());
            }

            // 通过率范围查询
            if (queryCondition.getPassRate() != null) {
                queryWrapper.ge(DailyInspectionReportDO::getPassRate, queryCondition.getPassRate());
            }

            // 报告状态查询
            if (StringUtils.isNotBlank(queryCondition.getReportStatus())) {
                queryWrapper.eq(DailyInspectionReportDO::getReportStatus, queryCondition.getReportStatus());
            }
        }

        // 按报告日期倒序排列
        queryWrapper.orderByDesc(DailyInspectionReportDO::getReportDate);

        // 执行分页查询
        Page<DailyInspectionReportDO> pageParam = new Page<>(page.getCurrent(), page.getSize());
        IPage<DailyInspectionReportDO> result = dailyInspectionReportRepository.page(pageParam, queryWrapper);

        log.info("Found {} daily inspection reports", result.getTotal());
        return result;
    }

    @Override
    public DailyInspectionReportDO getReportDetail(String reportId) {
        log.info("Getting daily inspection report detail for reportId: {}", reportId);

        if (StringUtils.isBlank(reportId)) {
            log.warn("Report ID is blank");
            return null;
        }

        DailyInspectionReportDO report = dailyInspectionReportRepository.getOne(
                Wrappers.lambdaQuery(DailyInspectionReportDO.class)
                        .eq(DailyInspectionReportDO::getReportId, reportId)
        );

        if (report == null) {
            log.warn("Daily inspection report not found for reportId: {}", reportId);
        }

        return report;
    }

    @Override
    public DailyInspectionReportDetailDO getReportDetailContent(String reportId) {
        log.info("Getting daily inspection report detail content for reportId: {}", reportId);

        if (StringUtils.isBlank(reportId)) {
            log.warn("Report ID is blank");
            return null;
        }

        DailyInspectionReportDetailDO reportDetail = dailyInspectionReportDetailRepository.getOne(
                Wrappers.lambdaQuery(DailyInspectionReportDetailDO.class)
                        .eq(DailyInspectionReportDetailDO::getReportId, reportId)
        );

        if (reportDetail == null) {
            log.warn("Daily inspection report detail content not found for reportId: {}", reportId);
        }

        return reportDetail;
    }
}
