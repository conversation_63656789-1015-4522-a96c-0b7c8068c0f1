package com.cmpay.hacp.inspection.application.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.cmpay.hacp.inspection.application.service.PerInspectionReportService;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PerInspectionReportDO;
import com.cmpay.hacp.inspection.infrastructure.database.dataobject.PerInspectionReportDetailDO;
import com.cmpay.hacp.inspection.infrastructure.repository.PerInspectionReportDetailRepository;
import com.cmpay.hacp.inspection.infrastructure.repository.PerInspectionReportRepository;
import com.cmpay.lemon.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 按次巡检报告查询服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PerInspectionReportServiceImpl implements PerInspectionReportService {

    private final PerInspectionReportRepository perInspectionReportRepository;
    private final PerInspectionReportDetailRepository perInspectionReportDetailRepository;

    @Override
    public IPage<PerInspectionReportDO> getReportPage(PageDTO<?> page, PerInspectionReportDO queryCondition) {
        log.info("Querying per-inspection reports with page: {}, condition: {}", page, queryCondition);

        // 构建查询条件
        var queryWrapper = Wrappers.lambdaQuery(PerInspectionReportDO.class);

        if (queryCondition != null) {
            // 报告ID模糊查询
            if (StringUtils.isNotBlank(queryCondition.getReportId())) {
                queryWrapper.like(PerInspectionReportDO::getReportId, queryCondition.getReportId());
            }

            // 任务ID精确查询
            if (StringUtils.isNotBlank(queryCondition.getTaskId())) {
                queryWrapper.eq(PerInspectionReportDO::getTaskId, queryCondition.getTaskId());
            }

            // 任务名称模糊查询
            if (StringUtils.isNotBlank(queryCondition.getTaskName())) {
                queryWrapper.like(PerInspectionReportDO::getTaskName, queryCondition.getTaskName());
            }

            // 触发方式查询
            if (queryCondition.getTriggerMode() != null) {
                queryWrapper.eq(PerInspectionReportDO::getTriggerMode, queryCondition.getTriggerMode());
            }

            // 执行状态查询
            if (queryCondition.getExecutionStatus() != null) {
                queryWrapper.eq(PerInspectionReportDO::getExecutionStatus, queryCondition.getExecutionStatus());
            }

            // 开始时间范围查询
            if (queryCondition.getStartTime() != null) {
                queryWrapper.ge(PerInspectionReportDO::getStartTime, queryCondition.getStartTime());
            }

            // 结束时间范围查询
            if (queryCondition.getEndTime() != null) {
                queryWrapper.le(PerInspectionReportDO::getEndTime, queryCondition.getEndTime());
            }
        }

        // 按生成时间倒序排列
        queryWrapper.orderByDesc(PerInspectionReportDO::getGenerateTime);

        // 执行分页查询
        Page<PerInspectionReportDO> pageParam = new Page<>(page.getCurrent(), page.getSize());
        IPage<PerInspectionReportDO> result = perInspectionReportRepository.page(pageParam, queryWrapper);

        log.info("Found {} per-inspection reports", result.getTotal());
        return result;
    }

    @Override
    public PerInspectionReportDO getReportDetail(String reportId) {
        log.info("Getting per-inspection report detail for reportId: {}", reportId);

        if (StringUtils.isBlank(reportId)) {
            log.warn("Report ID is blank");
            return null;
        }

        PerInspectionReportDO report = perInspectionReportRepository.getOne(
                Wrappers.lambdaQuery(PerInspectionReportDO.class)
                        .eq(PerInspectionReportDO::getReportId, reportId)
        );

        if (report == null) {
            log.warn("Per-inspection report not found for reportId: {}", reportId);
        }

        return report;
    }

    @Override
    public PerInspectionReportDetailDO getReportDetailContent(String reportId) {
        log.info("Getting per-inspection report detail content for reportId: {}", reportId);

        if (StringUtils.isBlank(reportId)) {
            log.warn("Report ID is blank");
            return null;
        }

        PerInspectionReportDetailDO reportDetail = perInspectionReportDetailRepository.getOne(
                Wrappers.lambdaQuery(PerInspectionReportDetailDO.class)
                        .eq(PerInspectionReportDetailDO::getReportId, reportId)
        );

        if (reportDetail == null) {
            log.warn("Per-inspection report detail content not found for reportId: {}", reportId);
        }

        return reportDetail;
    }
}
