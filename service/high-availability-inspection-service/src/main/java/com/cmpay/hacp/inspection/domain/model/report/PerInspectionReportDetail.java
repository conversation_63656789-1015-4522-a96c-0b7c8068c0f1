package com.cmpay.hacp.inspection.domain.model.report;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 按次巡检报告详细内容领域对象
 */
@Data
public class PerInspectionReportDetail {

    /**
     * 报告ID
     */
    private String reportId;

    /**
     * 执行概况
     */
    private ExecutionSummary executionSummary;

    /**
     * 执行结果分布（饼图数据）
     */
    private ExecutionDistribution executionDistribution;

    /**
     * 执行时间分布（柱状图数据）
     */
    private ExecutionTimeDistribution timeDistribution;

    /**
     * 规则检查详情列表
     */
    private List<RuleCheckDetail> ruleCheckDetails;

    /**
     * 异常详情列表（问题汇总）
     */
    private List<ExceptionDetail> exceptionDetails;

    /**
     * 执行概况
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionSummary {
        /**
         * 开始时间
         */
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        private LocalDateTime endTime;

        /**
         * 执行耗时（秒）
         */
        private Long duration;

        /**
         * 执行耗时字符串（如：38秒）
         */
        private String durationStr;

        /**
         * 规则总数
         */
        private Integer totalRules;

        /**
         * 成功规则数
         */
        private Integer successRules;
    }

    /**
     * 执行结果分布（饼图数据）
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionDistribution {
        /**
         * 总检查数
         */
        private Integer total;

        /**
         * 通过率百分比
         */
        private Integer successRate;

        /**
         * 各状态数量
         */
        private List<StatusCount> statusCounts;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class StatusCount {
            /**
             * 状态名称（通过、告警、错误）
             */
            private String status;

            /**
             * 状态编码（passed、warning、failed）
             */
            private String statusCode;

            /**
             * 数量
             */
            private Integer count;
        }
    }

    /**
     * 执行时间分布（柱状图数据）
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionTimeDistribution {
        /**
         * 时间区间数据
         */
        private List<TimeRange> timeRanges;

        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class TimeRange {
            /**
             * 时间区间标签（<1秒、1-3秒、3-5秒、5-10秒、>10秒）
             */
            private String label;

            /**
             * 该区间的数量
             */
            private Integer count;
        }
    }

    /**
     * 规则检查详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RuleCheckDetail {
        /**
         * 规则ID
         */
        private String ruleId;

        /**
         * 规则名称
         */
        private String ruleName;

        /**
         * 资源名称
         */
        private String resourceName;

        /**
         * 执行状态
         */
        private String executionStatus;

        /**
         * 执行结果
         */
        private String executionResult;

        /**
         * 执行时间
         */
        private LocalDateTime executionTime;

        /**
         * 执行耗时（毫秒）
         */
        private Long executionDuration;
    }

    /**
     * 异常详情（问题汇总）
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExceptionDetail {
        /**
         * 规则ID
         */
        private String ruleId;

        /**
         * 规则名称
         */
        private String ruleName;

        /**
         * 异常描述
         */
        private String description;

        /**
         * 资源名称
         */
        private String resourceName;

        /**
         * 建议措施
         */
        private String suggestion;
    }
}
